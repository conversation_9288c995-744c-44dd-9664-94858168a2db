"""
主程序 - 自动化注册 Augment Code 账户
"""

import time
import sys
from datetime import datetime
from loguru import logger

from config import LOG_CONFIG
from email_handler import EmailHandler
from web_automation import WebAutomation
from utils import (
    retry_operation, log_step, save_registration_info, 
    get_registration_statistics, format_duration
)


def setup_logging():
    """设置日志"""
    logger.remove()  # 移除默认处理器
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format=LOG_CONFIG['format'],
        level=LOG_CONFIG['level'],
        colorize=True
    )
    
    # 添加文件输出
    logger.add(
        LOG_CONFIG['file'],
        format=LOG_CONFIG['format'],
        level=LOG_CONFIG['level'],
        rotation="10 MB",
        retention="7 days"
    )


def register_single_account() -> bool:
    """注册单个账户"""
    start_time = time.time()
    email = None
    success = False
    
    try:
        log_step("开始自动化注册流程")
        
        # 1. 启动网页自动化
        log_step("启动浏览器", "初始化网页自动化模块")
        with WebAutomation() as web_auto:
            
            # 2. 生成随机邮箱
            log_step("生成随机邮箱")
            email = web_auto.generate_random_email()
            
            # 3. 访问注册页面
            log_step("访问注册页面")
            if not web_auto.navigate_to_register_page():
                logger.error("无法访问注册页面")
                return False
            
            # 4. 填写注册表单
            log_step("填写注册表单", f"邮箱: {email}")
            if not web_auto.fill_registration_form(email):
                logger.error("填写注册表单失败")
                web_auto.take_screenshot("form_fill_error.png")
                return False
            
            # 5. 等待并获取验证码
            log_step("获取邮箱验证码", "连接QQ邮箱获取验证码")
            with EmailHandler() as email_handler:
                verification_code = email_handler.get_latest_verification_code(email)
                
                if not verification_code:
                    logger.error("未能获取验证码")
                    web_auto.take_screenshot("verification_code_error.png")
                    return False
            
            # 6. 填写验证码
            log_step("填写验证码", f"验证码: {verification_code}")
            if not web_auto.fill_verification_code(verification_code):
                logger.error("填写验证码失败")
                web_auto.take_screenshot("code_fill_error.png")
                return False
            
            # 7. 等待注册完成
            log_step("等待注册完成")
            if web_auto.wait_for_success_page():
                success = True
                logger.success(f"注册成功! 邮箱: {email}")
                web_auto.take_screenshot("registration_success.png")
            else:
                logger.warning("无法确认注册是否成功，请手动检查")
                web_auto.take_screenshot("registration_uncertain.png")
                success = True  # 假设成功，因为可能只是检测逻辑问题
    
    except Exception as e:
        logger.error(f"注册过程中出现异常: {e}")
        success = False
    
    finally:
        # 记录注册信息
        if email:
            save_registration_info(email, success)
        
        # 记录耗时
        duration = time.time() - start_time
        log_step("注册流程完成", f"耗时: {format_duration(duration)}")
    
    return success


def register_multiple_accounts(count: int, delay_between: int = 30):
    """注册多个账户"""
    log_step("批量注册开始", f"目标数量: {count}个账户")
    
    success_count = 0
    failed_count = 0
    
    for i in range(count):
        logger.info(f"\n{'='*60}")
        logger.info(f"开始注册第 {i+1}/{count} 个账户")
        logger.info(f"{'='*60}")
        
        try:
            if register_single_account():
                success_count += 1
                logger.success(f"第 {i+1} 个账户注册成功")
            else:
                failed_count += 1
                logger.error(f"第 {i+1} 个账户注册失败")
            
            # 如果不是最后一个账户，等待一段时间
            if i < count - 1:
                logger.info(f"等待 {delay_between} 秒后继续下一个账户...")
                time.sleep(delay_between)
                
        except KeyboardInterrupt:
            logger.warning("用户中断了批量注册流程")
            break
        except Exception as e:
            logger.error(f"第 {i+1} 个账户注册时出现异常: {e}")
            failed_count += 1
    
    # 显示最终统计
    log_step("批量注册完成")
    logger.info(f"成功: {success_count} 个")
    logger.info(f"失败: {failed_count} 个")
    logger.info(f"成功率: {(success_count/(success_count+failed_count)*100):.1f}%")
    
    # 显示总体统计
    stats = get_registration_statistics()
    logger.info(f"\n总体统计:")
    logger.info(f"总计: {stats['total']} 个账户")
    logger.info(f"成功: {stats['success']} 个")
    logger.info(f"失败: {stats['failed']} 个")
    logger.info(f"总成功率: {stats['success_rate']}")


def main():
    """主函数"""
    setup_logging()
    
    logger.info("=" * 60)
    logger.info("Augment Code 自动化注册程序")
    logger.info("=" * 60)
    
    try:
        # 显示当前统计
        stats = get_registration_statistics()
        if stats['total'] > 0:
            logger.info(f"历史统计: 总计 {stats['total']} 个账户，成功率 {stats['success_rate']}")
        
        # 询问用户要注册多少个账户
        while True:
            try:
                count = input("\n请输入要注册的账户数量 (输入 0 退出): ")
                count = int(count)
                
                if count == 0:
                    logger.info("程序退出")
                    return
                elif count < 0:
                    logger.warning("请输入正数")
                    continue
                else:
                    break
            except ValueError:
                logger.warning("请输入有效的数字")
        
        # 询问账户间隔时间
        while True:
            try:
                delay = input(f"\n请输入账户间隔时间（秒，默认30秒）: ").strip()
                if not delay:
                    delay = 30
                else:
                    delay = int(delay)
                
                if delay < 0:
                    logger.warning("间隔时间不能为负数")
                    continue
                else:
                    break
            except ValueError:
                logger.warning("请输入有效的数字")
        
        # 开始注册
        if count == 1:
            register_single_account()
        else:
            register_multiple_accounts(count, delay)
    
    except KeyboardInterrupt:
        logger.warning("\n程序被用户中断")
    except Exception as e:
        logger.error(f"程序运行时出现异常: {e}")
    
    logger.info("程序结束")


if __name__ == "__main__":
    main()
