"""
组件测试脚本 - 用于测试各个模块的功能
"""

import sys
import time
from loguru import logger

from config import QQ_EMAIL_CONFIG, TARGET_EMAIL_DOMAIN
from email_handler import EmailHandler
from web_automation import WebAutomation
from utils import validate_email, get_registration_statistics


def test_email_connection():
    """测试邮箱连接"""
    logger.info("=" * 50)
    logger.info("测试邮箱连接")
    logger.info("=" * 50)
    
    try:
        with EmailHandler() as email_handler:
            if email_handler.connected:
                logger.success("✅ 邮箱连接成功")
                return True
            else:
                logger.error("❌ 邮箱连接失败")
                return False
    except Exception as e:
        logger.error(f"❌ 邮箱连接测试异常: {e}")
        return False


def test_browser_automation():
    """测试浏览器自动化"""
    logger.info("=" * 50)
    logger.info("测试浏览器自动化")
    logger.info("=" * 50)
    
    try:
        with WebAutomation() as web_auto:
            # 测试浏览器启动
            logger.info("测试浏览器启动...")
            if not web_auto.page:
                logger.error("❌ 浏览器启动失败")
                return False
            
            logger.success("✅ 浏览器启动成功")
            
            # 测试随机邮箱生成
            logger.info("测试随机邮箱生成...")
            email = web_auto.generate_random_email()
            if validate_email(email) and TARGET_EMAIL_DOMAIN in email:
                logger.success(f"✅ 随机邮箱生成成功: {email}")
            else:
                logger.error(f"❌ 随机邮箱生成失败: {email}")
                return False
            
            # 测试页面访问
            logger.info("测试页面访问...")
            if web_auto.navigate_to_register_page():
                logger.success("✅ 页面访问成功")
                web_auto.take_screenshot("test_page_access.png")
            else:
                logger.error("❌ 页面访问失败")
                return False
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 浏览器自动化测试异常: {e}")
        return False


def test_email_generation():
    """测试邮箱生成"""
    logger.info("=" * 50)
    logger.info("测试邮箱生成")
    logger.info("=" * 50)
    
    try:
        web_auto = WebAutomation()
        
        # 生成多个邮箱测试
        emails = []
        for i in range(5):
            email = web_auto.generate_random_email()
            emails.append(email)
            logger.info(f"生成邮箱 {i+1}: {email}")
        
        # 检查邮箱格式和唯一性
        valid_count = 0
        for email in emails:
            if validate_email(email):
                valid_count += 1
        
        unique_count = len(set(emails))
        
        logger.info(f"有效邮箱数量: {valid_count}/5")
        logger.info(f"唯一邮箱数量: {unique_count}/5")
        
        if valid_count == 5 and unique_count == 5:
            logger.success("✅ 邮箱生成测试通过")
            return True
        else:
            logger.error("❌ 邮箱生成测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 邮箱生成测试异常: {e}")
        return False


def test_configuration():
    """测试配置"""
    logger.info("=" * 50)
    logger.info("测试配置")
    logger.info("=" * 50)
    
    try:
        # 检查邮箱配置
        logger.info("检查邮箱配置...")
        required_keys = ['imap_server', 'imap_port', 'email', 'password']
        for key in required_keys:
            if key not in QQ_EMAIL_CONFIG or not QQ_EMAIL_CONFIG[key]:
                logger.error(f"❌ 邮箱配置缺少: {key}")
                return False
        
        logger.success("✅ 邮箱配置检查通过")
        
        # 检查目标邮箱域名
        logger.info("检查目标邮箱域名...")
        if not TARGET_EMAIL_DOMAIN or not TARGET_EMAIL_DOMAIN.startswith('@'):
            logger.error(f"❌ 目标邮箱域名配置错误: {TARGET_EMAIL_DOMAIN}")
            return False
        
        logger.success(f"✅ 目标邮箱域名: {TARGET_EMAIL_DOMAIN}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置测试异常: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始运行组件测试")
    logger.info("=" * 60)
    
    tests = [
        ("配置测试", test_configuration),
        ("邮箱生成测试", test_email_generation),
        ("邮箱连接测试", test_email_connection),
        ("浏览器自动化测试", test_browser_automation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n开始 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.success(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # 测试间隔
    
    # 显示测试结果汇总
    logger.info("\n" + "=" * 60)
    logger.info("测试结果汇总")
    logger.info("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        logger.success("🎉 所有测试通过！程序可以正常运行")
    else:
        logger.warning("⚠️  部分测试失败，请检查配置和环境")
    
    # 显示统计信息
    stats = get_registration_statistics()
    if stats['total'] > 0:
        logger.info(f"\n历史注册统计:")
        logger.info(f"总计: {stats['total']} 个账户")
        logger.info(f"成功: {stats['success']} 个")
        logger.info(f"成功率: {stats['success_rate']}")


def main():
    """主函数"""
    # 设置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="{time:HH:mm:ss} | {level} | {message}",
        level="INFO",
        colorize=True
    )
    
    logger.info("Augment Code 自动化注册程序 - 组件测试")
    
    try:
        run_all_tests()
    except KeyboardInterrupt:
        logger.warning("\n测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
