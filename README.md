# Augment Code 自动化注册程序

这是一个自动化注册 Augment Code 账户的程序，支持批量注册和邮箱验证码自动获取。

## 功能特性

- 🤖 **全自动注册**: 自动填写表单、获取验证码、完成注册
- 📧 **邮箱验证**: 自动连接QQ邮箱获取验证码
- 🎲 **随机邮箱**: 自动生成随机邮箱地址
- 📊 **统计记录**: 记录注册成功率和详细日志
- 🔄 **批量处理**: 支持批量注册多个账户
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 📸 **截图记录**: 自动截图记录关键步骤

## 环境要求

- Python 3.8+
- Windows/macOS/Linux
- 稳定的网络连接

## 安装步骤

### 1. 安装 Python 依赖

```bash
pip install -r requirements.txt
```

### 2. 安装 Playwright 浏览器

```bash
playwright install chromium
```

### 3. 配置邮箱设置

编辑 `config.py` 文件中的邮箱配置：

```python
QQ_EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',  # 您的QQ邮箱
    'password': 'your_auth_code',     # QQ邮箱授权码
    'use_ssl': True
}
```

**重要**: 请确保：
- 已开启QQ邮箱的IMAP服务
- 使用的是QQ邮箱授权码，不是QQ密码
- 邮箱配置正确

### 4. QQ邮箱授权码获取方法

1. 登录QQ邮箱网页版
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 生成授权码并记录

## 使用方法

### 运行程序

```bash
python main.py
```

### 程序流程

1. **启动程序**: 运行后会显示历史统计信息
2. **输入数量**: 输入要注册的账户数量
3. **设置间隔**: 设置账户间的等待时间（避免频率过高）
4. **自动执行**: 程序会自动完成以下步骤：
   - 生成随机邮箱地址
   - 访问注册页面
   - 填写注册表单
   - 获取邮箱验证码
   - 填写验证码
   - 完成注册

### 配置选项

在 `config.py` 中可以调整以下设置：

- **浏览器模式**: `headless=False` 显示浏览器窗口，`True` 为无头模式
- **等待时间**: 调整各种操作的等待时间
- **邮箱前缀**: 设置随机邮箱前缀的长度和字符类型
- **超时设置**: 设置页面加载和操作的超时时间

## 文件说明

- `main.py` - 主程序入口
- `config.py` - 配置文件
- `email_handler.py` - 邮箱处理模块
- `web_automation.py` - 网页自动化模块
- `utils.py` - 工具函数
- `requirements.txt` - Python依赖列表
- `automation.log` - 程序运行日志
- `registration_log.json` - 注册记录文件

## 注意事项

### 安全提醒
- 请妥善保管QQ邮箱授权码
- 不要在公共场所运行程序
- 定期更换邮箱授权码

### 使用限制
- 请遵守网站的使用条款
- 避免过于频繁的注册请求
- 建议在账户间设置适当的间隔时间

### 故障排除

**常见问题**:

1. **邮箱连接失败**
   - 检查QQ邮箱IMAP服务是否开启
   - 确认授权码是否正确
   - 检查网络连接

2. **页面元素找不到**
   - 网站可能更新了页面结构
   - 检查网络连接是否稳定
   - 尝试增加等待时间

3. **验证码获取失败**
   - 检查邮箱配置
   - 确认验证码邮件是否发送到QQ邮箱
   - 增加验证码等待时间

## 日志和统计

程序会自动记录：
- 详细的操作日志 (`automation.log`)
- 注册成功/失败统计 (`registration_log.json`)
- 关键步骤的截图 (`screenshot_*.png`)

## 技术支持

如果遇到问题，请检查：
1. 日志文件中的错误信息
2. 截图文件中的页面状态
3. 网络连接和邮箱配置

## 免责声明

本程序仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用者需自行承担使用风险。
