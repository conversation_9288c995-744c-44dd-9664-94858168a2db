"""
工具函数模块 - 包含各种辅助函数
"""

import time
import random
from typing import List, Dict, Any
from loguru import logger


def retry_operation(func, max_retries: int = 3, delay: float = 1.0, *args, **kwargs):
    """
    重试操作装饰器
    
    Args:
        func: 要重试的函数
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
        *args, **kwargs: 传递给函数的参数
    
    Returns:
        函数执行结果
    """
    for attempt in range(max_retries):
        try:
            result = func(*args, **kwargs)
            if result:
                return result
        except Exception as e:
            logger.warning(f"操作失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            
        if attempt < max_retries - 1:
            time.sleep(delay)
    
    logger.error(f"操作在{max_retries}次尝试后仍然失败")
    return None


def wait_with_timeout(condition_func, timeout: float = 30.0, check_interval: float = 1.0):
    """
    等待条件满足，带超时
    
    Args:
        condition_func: 条件检查函数，返回True表示条件满足
        timeout: 超时时间（秒）
        check_interval: 检查间隔（秒）
    
    Returns:
        bool: 条件是否在超时前满足
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            if condition_func():
                return True
        except Exception as e:
            logger.debug(f"条件检查时出错: {e}")
        
        time.sleep(check_interval)
    
    return False


def generate_user_agent() -> str:
    """生成随机User-Agent"""
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ]
    
    return random.choice(user_agents)


def log_step(step_name: str, details: str = ""):
    """记录步骤日志"""
    separator = "=" * 50
    logger.info(f"\n{separator}")
    logger.info(f"步骤: {step_name}")
    if details:
        logger.info(f"详情: {details}")
    logger.info(f"{separator}")


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def save_registration_info(email: str, success: bool, timestamp: str = None):
    """保存注册信息到文件"""
    import json
    import os
    from datetime import datetime
    
    if not timestamp:
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    registration_data = {
        'email': email,
        'success': success,
        'timestamp': timestamp
    }
    
    # 读取现有数据
    filename = 'registration_log.json'
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except:
            data = []
    else:
        data = []
    
    # 添加新记录
    data.append(registration_data)
    
    # 保存数据
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"注册信息已保存到 {filename}")
    except Exception as e:
        logger.error(f"保存注册信息失败: {e}")


def get_registration_statistics():
    """获取注册统计信息"""
    import json
    import os
    
    filename = 'registration_log.json'
    if not os.path.exists(filename):
        return {'total': 0, 'success': 0, 'failed': 0}
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total = len(data)
        success = sum(1 for record in data if record.get('success', False))
        failed = total - success
        
        return {
            'total': total,
            'success': success,
            'failed': failed,
            'success_rate': f"{(success/total*100):.1f}%" if total > 0 else "0%"
        }
    except Exception as e:
        logger.error(f"读取注册统计信息失败: {e}")
        return {'total': 0, 'success': 0, 'failed': 0}


def clean_old_logs(days: int = 7):
    """清理旧日志文件"""
    import os
    import glob
    from datetime import datetime, timedelta
    
    try:
        # 清理截图文件
        screenshot_files = glob.glob("screenshot_*.png")
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for file in screenshot_files:
            file_time = datetime.fromtimestamp(os.path.getmtime(file))
            if file_time < cutoff_time:
                os.remove(file)
                logger.info(f"已删除旧截图: {file}")
        
        logger.info(f"清理了{days}天前的旧文件")
        
    except Exception as e:
        logger.error(f"清理旧文件失败: {e}")


def format_duration(seconds: float) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"
