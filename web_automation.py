"""
网页自动化模块 - 负责网页操作和表单填写
"""

import random
import string
import time
from typing import Optional
from playwright.sync_api import sync_playwright, <PERSON>, Browser
from loguru import logger
from config import BROWSER_CONFIG, REGISTER_URL, TARGET_EMAIL_DOMAIN, EMAIL_PREFIX_CONFIG, WAIT_TIMES


class WebAutomation:
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.page = None
        
    def start_browser(self) -> bool:
        """启动浏览器"""
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(
                headless=BROWSER_CONFIG['headless']
            )
            
            context = self.browser.new_context(
                viewport=BROWSER_CONFIG['viewport']
            )
            
            self.page = context.new_page()
            self.page.set_default_timeout(BROWSER_CONFIG['timeout'])
            
            logger.info("浏览器启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.close()
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器时出错: {e}")
    
    def generate_random_email(self) -> str:
        """生成随机邮箱地址"""
        length = EMAIL_PREFIX_CONFIG['length']
        chars = ''
        
        if EMAIL_PREFIX_CONFIG['include_letters']:
            chars += string.ascii_lowercase
        if EMAIL_PREFIX_CONFIG['include_numbers']:
            chars += string.digits
        
        random_prefix = ''.join(random.choice(chars) for _ in range(length))
        email = random_prefix + TARGET_EMAIL_DOMAIN
        
        logger.info(f"生成随机邮箱: {email}")
        return email
    
    def navigate_to_register_page(self) -> bool:
        """导航到注册页面"""
        try:
            logger.info(f"正在访问注册页面: {REGISTER_URL}")
            self.page.goto(REGISTER_URL)
            
            # 等待页面加载
            time.sleep(WAIT_TIMES['page_load'])
            
            logger.info("成功访问注册页面")
            return True
            
        except Exception as e:
            logger.error(f"访问注册页面失败: {e}")
            return False
    
    def fill_registration_form(self, email: str) -> bool:
        """填写注册表单"""
        try:
            logger.info(f"开始填写注册表单，邮箱: {email}")
            
            # 等待页面完全加载
            time.sleep(WAIT_TIMES['form_fill'])
            
            # 查找邮箱输入框（可能的选择器）
            email_selectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[id="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="邮箱"]',
                '#email',
                '.email-input',
                '[data-testid="email"]'
            ]
            
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.page.wait_for_selector(selector, timeout=5000)
                    if email_input:
                        logger.info(f"找到邮箱输入框: {selector}")
                        break
                except:
                    continue
            
            if not email_input:
                logger.error("未找到邮箱输入框")
                return False
            
            # 填写邮箱
            email_input.fill(email)
            logger.info("邮箱填写完成")
            
            # 查找并点击提交按钮
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("注册")',
                'button:has-text("Register")',
                'button:has-text("Sign up")',
                'button:has-text("Continue")',
                'button:has-text("Next")',
                '.submit-btn',
                '.register-btn'
            ]
            
            submit_button = None
            for selector in submit_selectors:
                try:
                    submit_button = self.page.wait_for_selector(selector, timeout=5000)
                    if submit_button:
                        logger.info(f"找到提交按钮: {selector}")
                        break
                except:
                    continue
            
            if submit_button:
                submit_button.click()
                logger.info("已点击提交按钮")
                time.sleep(WAIT_TIMES['form_fill'])
                return True
            else:
                logger.warning("未找到提交按钮，尝试按Enter键")
                email_input.press('Enter')
                time.sleep(WAIT_TIMES['form_fill'])
                return True
                
        except Exception as e:
            logger.error(f"填写注册表单失败: {e}")
            return False
    
    def fill_verification_code(self, verification_code: str) -> bool:
        """填写验证码"""
        try:
            logger.info(f"开始填写验证码: {verification_code}")
            
            # 查找验证码输入框
            code_selectors = [
                'input[name="code"]',
                'input[id="code"]',
                'input[type="text"][placeholder*="code"]',
                'input[type="text"][placeholder*="验证码"]',
                'input[placeholder*="verification"]',
                '#verification-code',
                '.verification-input',
                '[data-testid="verification-code"]'
            ]
            
            code_input = None
            for selector in code_selectors:
                try:
                    code_input = self.page.wait_for_selector(selector, timeout=10000)
                    if code_input:
                        logger.info(f"找到验证码输入框: {selector}")
                        break
                except:
                    continue
            
            if not code_input:
                logger.error("未找到验证码输入框")
                return False
            
            # 填写验证码
            code_input.fill(verification_code)
            logger.info("验证码填写完成")
            
            # 查找并点击确认按钮
            confirm_selectors = [
                'button[type="submit"]',
                'button:has-text("确认")',
                'button:has-text("Verify")',
                'button:has-text("Continue")',
                'button:has-text("Complete")',
                '.verify-btn',
                '.confirm-btn'
            ]
            
            confirm_button = None
            for selector in confirm_selectors:
                try:
                    confirm_button = self.page.wait_for_selector(selector, timeout=5000)
                    if confirm_button:
                        logger.info(f"找到确认按钮: {selector}")
                        break
                except:
                    continue
            
            if confirm_button:
                confirm_button.click()
                logger.info("已点击确认按钮")
            else:
                logger.warning("未找到确认按钮，尝试按Enter键")
                code_input.press('Enter')
            
            time.sleep(WAIT_TIMES['form_fill'])
            return True
            
        except Exception as e:
            logger.error(f"填写验证码失败: {e}")
            return False
    
    def wait_for_success_page(self) -> bool:
        """等待成功页面"""
        try:
            # 等待可能的成功指示器
            success_indicators = [
                'text=Welcome',
                'text=Success',
                'text=成功',
                'text=注册成功',
                '.success',
                '.welcome'
            ]
            
            for indicator in success_indicators:
                try:
                    element = self.page.wait_for_selector(indicator, timeout=10000)
                    if element:
                        logger.info("检测到注册成功页面")
                        return True
                except:
                    continue
            
            # 如果没有明确的成功指示器，检查URL变化
            current_url = self.page.url
            if 'success' in current_url.lower() or 'welcome' in current_url.lower():
                logger.info("根据URL变化判断注册成功")
                return True
            
            logger.warning("未能确认注册是否成功")
            return False
            
        except Exception as e:
            logger.error(f"等待成功页面时出错: {e}")
            return False
    
    def take_screenshot(self, filename: str = None):
        """截图保存"""
        try:
            if not filename:
                filename = f"screenshot_{int(time.time())}.png"
            
            self.page.screenshot(path=filename)
            logger.info(f"截图已保存: {filename}")
            
        except Exception as e:
            logger.error(f"截图失败: {e}")
    
    def __enter__(self):
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close_browser()
