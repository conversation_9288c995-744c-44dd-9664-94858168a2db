"""
配置文件 - 包含所有必要的配置信息
"""

# QQ邮箱配置
QQ_EMAIL_CONFIG = {
    'imap_server': 'imap.qq.com',
    'imap_port': 993,
    'email': '<EMAIL>',  # 您的QQ邮箱
    'password': 'chbqkqrcmvpzcjai',  # QQ邮箱授权码
    'use_ssl': True
}

# 目标邮箱域名
TARGET_EMAIL_DOMAIN = '@zhangfei.cloudns.ch'

# 注册网站配置
REGISTER_URL = 'https://login.augmentcode.com/u/login/identifier?state=hKFo2SBSWk81OFJBakhpX0NHTV9WZHR0SXptWG1yS29BNGhWcaFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGpTTWJkc0V1bnBnR29nWm5zWFNvMUhnMVRFNEVvWkdIo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE'

# 浏览器配置
BROWSER_CONFIG = {
    'headless': False,  # 设置为True可以无头模式运行
    'timeout': 30000,   # 30秒超时
    'viewport': {'width': 1280, 'height': 720}
}

# 等待时间配置（秒）
WAIT_TIMES = {
    'page_load': 5,
    'email_check': 10,
    'form_fill': 2,
    'verification_code': 60  # 等待验证码邮件的最大时间
}

# 随机邮箱前缀配置
EMAIL_PREFIX_CONFIG = {
    'length': 8,  # 随机前缀长度
    'include_numbers': True,
    'include_letters': True
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}',
    'file': 'automation.log'
}
