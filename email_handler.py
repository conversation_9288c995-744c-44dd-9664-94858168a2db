"""
邮箱处理模块 - 负责连接QQ邮箱并获取验证码
"""

import imaplib
import email
import re
import time
from typing import Optional
from loguru import logger
from config import QQ_EMAIL_CONFIG, WAIT_TIMES


class EmailHandler:
    def __init__(self):
        self.imap_server = None
        self.connected = False
        
    def connect(self) -> bool:
        """连接到QQ邮箱"""
        try:
            self.imap_server = imaplib.IMAP4_SSL(
                QQ_EMAIL_CONFIG['imap_server'], 
                QQ_EMAIL_CONFIG['imap_port']
            )
            
            self.imap_server.login(
                QQ_EMAIL_CONFIG['email'], 
                QQ_EMAIL_CONFIG['password']
            )
            
            self.imap_server.select('INBOX')
            self.connected = True
            logger.info("成功连接到QQ邮箱")
            return True
            
        except Exception as e:
            logger.error(f"连接QQ邮箱失败: {e}")
            return False
    
    def disconnect(self):
        """断开邮箱连接"""
        if self.imap_server and self.connected:
            try:
                self.imap_server.close()
                self.imap_server.logout()
                self.connected = False
                logger.info("已断开邮箱连接")
            except Exception as e:
                logger.error(f"断开邮箱连接时出错: {e}")
    
    def get_latest_verification_code(self, target_email: str, max_wait_time: int = None) -> Optional[str]:
        """
        获取最新的验证码
        
        Args:
            target_email: 目标邮箱地址
            max_wait_time: 最大等待时间（秒）
        
        Returns:
            验证码字符串，如果未找到则返回None
        """
        if not self.connected:
            if not self.connect():
                return None
        
        max_wait = max_wait_time or WAIT_TIMES['verification_code']
        start_time = time.time()
        
        logger.info(f"开始等待验证码邮件，目标邮箱: {target_email}")
        
        while time.time() - start_time < max_wait:
            try:
                # 搜索包含目标邮箱的邮件
                search_criteria = f'(TO "{target_email}" SUBJECT "verification")'
                status, messages = self.imap_server.search(None, search_criteria)
                
                if status == 'OK' and messages[0]:
                    # 获取最新邮件
                    latest_email_id = messages[0].split()[-1]
                    
                    # 获取邮件内容
                    status, msg_data = self.imap_server.fetch(latest_email_id, '(RFC822)')
                    
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        email_message = email.message_from_bytes(email_body)
                        
                        # 提取验证码
                        verification_code = self._extract_verification_code(email_message)
                        
                        if verification_code:
                            logger.info(f"成功获取验证码: {verification_code}")
                            return verification_code
                
                # 等待一段时间后重试
                time.sleep(5)
                logger.info("等待验证码邮件...")
                
            except Exception as e:
                logger.error(f"获取验证码时出错: {e}")
                time.sleep(5)
        
        logger.warning(f"在{max_wait}秒内未收到验证码邮件")
        return None
    
    def _extract_verification_code(self, email_message) -> Optional[str]:
        """从邮件中提取验证码"""
        try:
            # 获取邮件内容
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        body = part.get_payload(decode=True).decode('utf-8')
                        break
                    elif part.get_content_type() == "text/html":
                        body = part.get_payload(decode=True).decode('utf-8')
                        break
            else:
                body = email_message.get_payload(decode=True).decode('utf-8')
            
            # 使用正则表达式提取验证码
            # 常见的验证码模式
            patterns = [
                r'验证码[：:]\s*(\d{4,8})',
                r'verification code[：:]\s*(\d{4,8})',
                r'code[：:]\s*(\d{4,8})',
                r'(\d{6})',  # 6位数字
                r'(\d{4})',  # 4位数字
            ]
            
            for pattern in patterns:
                match = re.search(pattern, body, re.IGNORECASE)
                if match:
                    return match.group(1)
            
            logger.warning("未能从邮件中提取验证码")
            return None
            
        except Exception as e:
            logger.error(f"提取验证码时出错: {e}")
            return None
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()
