@echo off
chcp 65001 >nul
echo ========================================
echo Augment Code 自动化注册程序
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show playwright >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    echo 正在安装Playwright浏览器...
    playwright install chromium
)

echo 启动程序...
echo.
python main.py

echo.
echo 程序已结束
pause
